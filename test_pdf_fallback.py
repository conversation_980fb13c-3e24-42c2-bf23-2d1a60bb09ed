#!/usr/bin/env python3
"""
Test PDF processing fallback mechanism.

This script tests the PDF processing with both LlamaParse and PyMuPDF fallback.
"""

import os
import sys
import logging
import tempfile
from pathlib import Path

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(name)s: %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)


def create_simple_test_pdf():
    """Create a simple test PDF using reportlab."""
    try:
        from reportlab.lib.pagesizes import letter
        from reportlab.platypus import SimpleDocTemplate, Paragraph
        from reportlab.lib.styles import getSampleStyleSheet
        
        # Create temporary PDF
        with tempfile.NamedTemporaryFile(suffix='.pdf', delete=False) as tmp_file:
            pdf_path = tmp_file.name
        
        # Create document
        doc = SimpleDocTemplate(pdf_path, pagesize=letter)
        styles = getSampleStyleSheet()
        story = []
        
        # Add content
        story.append(Paragraph("Test Document", styles['Title']))
        story.append(Paragraph("Introduction", styles['Heading1']))
        story.append(Paragraph("""
        This is a simple test document to verify PDF processing functionality.
        It contains basic text that should be easily extractable by both
        LlamaParse and PyMuPDF fallback methods.
        """, styles['Normal']))
        
        story.append(Paragraph("Section 1: Basic Content", styles['Heading1']))
        story.append(Paragraph("""
        This section contains basic text content for testing the PDF
        processing pipeline. The text should be extracted and processed
        into semantic chunks for embedding generation.
        """, styles['Normal']))
        
        story.append(Paragraph("Section 2: Technical Details", styles['Heading1']))
        story.append(Paragraph("""
        This section discusses technical aspects of the document processing
        system including text extraction, chunking strategies, and
        embedding generation using modern AI models.
        """, styles['Normal']))
        
        # Build PDF
        doc.build(story)
        logger.info(f"Created test PDF: {pdf_path}")
        return pdf_path
        
    except ImportError:
        logger.error("reportlab not available. Install with: pip install reportlab")
        return None
    except Exception as e:
        logger.error(f"Error creating PDF: {e}")
        return None


def test_pymupdf_extraction(pdf_path):
    """Test PyMuPDF text extraction directly."""
    try:
        import fitz
        
        logger.info("Testing PyMuPDF text extraction...")
        doc = fitz.open(pdf_path)
        
        text = ""
        for page_num, page in enumerate(doc):
            page_text = page.get_text()
            if page_text.strip():
                text += f"\n\n--- Page {page_num + 1} ---\n\n"
                text += page_text
        
        doc.close()
        
        if text.strip():
            logger.info(f"✓ PyMuPDF extraction successful: {len(text)} characters")
            logger.info(f"Text preview: {text[:200]}...")
            return True
        else:
            logger.error("✗ PyMuPDF extraction failed: no text extracted")
            return False
            
    except ImportError:
        logger.error("PyMuPDF (fitz) not available")
        return False
    except Exception as e:
        logger.error(f"PyMuPDF extraction error: {e}")
        return False


def test_llamaparse_extraction(pdf_path):
    """Test LlamaParse extraction."""
    try:
        from llama_cloud_services import LlamaParse
        from llama_index.core import SimpleDirectoryReader
        
        logger.info("Testing LlamaParse extraction...")
        
        # Initialize parser
        parser = LlamaParse(
            result_type="markdown",
            num_workers=1,
            check_interval=2.0
        )
        
        # Parse document
        file_extractor = {".pdf": parser}
        documents = SimpleDirectoryReader(
            input_files=[pdf_path],
            file_extractor=file_extractor
        ).load_data()
        
        if documents:
            text = "\n".join([doc.text for doc in documents])
            logger.info(f"✓ LlamaParse extraction successful: {len(text)} characters")
            logger.info(f"Text preview: {text[:200]}...")
            return True
        else:
            logger.warning("✗ LlamaParse extraction failed: no documents returned")
            return False
            
    except ImportError:
        logger.warning("LlamaParse not available")
        return None
    except Exception as e:
        logger.warning(f"LlamaParse extraction error: {e}")
        return False


def test_pdf_processing_function(pdf_path):
    """Test the actual PDF processing function."""
    try:
        # Import the PDF processing function
        from processors.pdf import process_pdf
        from db import DatabaseConnection
        
        logger.info("Testing full PDF processing function...")
        
        # Create a test document record
        conn = DatabaseConnection()
        conn.connect()
        
        # Insert test document
        insert_query = """
        INSERT INTO ProcessingStatus (doc_id, file_path, currentStep, fileName)
        VALUES (%s, %s, %s, %s)
        """
        test_doc_id = 999999  # Use a test ID
        conn.execute_query(insert_query, (
            test_doc_id,
            pdf_path,
            "uploaded",
            os.path.basename(pdf_path)
        ))
        
        try:
            # Process the PDF
            process_pdf(test_doc_id)
            logger.info("✓ Full PDF processing successful")
            return True
            
        except Exception as e:
            logger.error(f"✗ Full PDF processing failed: {e}")
            return False
        finally:
            # Clean up test record
            conn.execute_query("DELETE FROM ProcessingStatus WHERE doc_id = %s", (test_doc_id,))
            conn.close()
            
    except Exception as e:
        logger.error(f"Error testing PDF processing function: {e}")
        return False


def main():
    """Main test function."""
    logger.info("=" * 60)
    logger.info("PDF Processing Fallback Test")
    logger.info("=" * 60)
    
    # Create test PDF
    pdf_path = create_simple_test_pdf()
    if not pdf_path:
        logger.error("Failed to create test PDF")
        return False
    
    try:
        # Test PyMuPDF extraction
        pymupdf_success = test_pymupdf_extraction(pdf_path)
        
        # Test LlamaParse extraction
        llamaparse_success = test_llamaparse_extraction(pdf_path)
        
        # Test full processing function
        full_processing_success = test_pdf_processing_function(pdf_path)
        
        # Summary
        logger.info("=" * 60)
        logger.info("Test Results Summary")
        logger.info("=" * 60)
        logger.info(f"PyMuPDF extraction: {'✓ PASS' if pymupdf_success else '✗ FAIL'}")
        logger.info(f"LlamaParse extraction: {'✓ PASS' if llamaparse_success else '✗ FAIL' if llamaparse_success is False else '- SKIP'}")
        logger.info(f"Full PDF processing: {'✓ PASS' if full_processing_success else '✗ FAIL'}")
        
        if pymupdf_success:
            logger.info("✓ Fallback mechanism should work")
        else:
            logger.error("✗ Fallback mechanism may not work")
        
        return pymupdf_success or llamaparse_success
        
    finally:
        # Clean up
        if os.path.exists(pdf_path):
            os.unlink(pdf_path)
            logger.info(f"Cleaned up test PDF: {pdf_path}")


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
