#!/usr/bin/env python3
"""
Comprehensive API Test Runner

This script orchestrates the complete testing of the PDF processing API,
including setup, PDF creation, and running both curl and Python tests.
"""

import os
import sys
import subprocess
import time
import logging
import argparse
from pathlib import Path

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)


class APITestRunner:
    """Comprehensive API test runner."""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.test_results = {}
    
    def check_dependencies(self) -> bool:
        """Check if required dependencies are available."""
        logger.info("Checking dependencies...")
        
        dependencies = {
            'python3': 'Python 3 interpreter',
            'curl': 'curl command-line tool',
        }
        
        missing = []
        for cmd, desc in dependencies.items():
            if not self.check_command(cmd):
                missing.append(f"{cmd} ({desc})")
        
        if missing:
            logger.error("Missing dependencies:")
            for dep in missing:
                logger.error(f"  - {dep}")
            return False
        
        logger.info("✓ All dependencies available")
        return True
    
    def check_command(self, command: str) -> bool:
        """Check if a command is available."""
        try:
            subprocess.run([command, '--version'], 
                         capture_output=True, check=False)
            return True
        except FileNotFoundError:
            return False
    
    def check_python_packages(self) -> bool:
        """Check if required Python packages are available."""
        logger.info("Checking Python packages...")
        
        packages = ['requests']
        missing = []
        
        for package in packages:
            try:
                __import__(package)
                logger.debug(f"✓ {package}")
            except ImportError:
                missing.append(package)
                logger.warning(f"✗ {package}")
        
        if missing:
            logger.warning("Missing Python packages:")
            for pkg in missing:
                logger.warning(f"  - {pkg}")
            logger.info("Install with: pip install " + " ".join(missing))
            return False
        
        logger.info("✓ All required Python packages available")
        return True
    
    def create_sample_pdf(self) -> bool:
        """Create a sample PDF for testing."""
        logger.info("Creating sample PDF...")
        
        try:
            result = subprocess.run([
                sys.executable, 'create_sample_pdf.py'
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                logger.info("✓ Sample PDF created successfully")
                return True
            else:
                logger.warning("Sample PDF creation had issues:")
                if result.stdout:
                    logger.warning(result.stdout)
                if result.stderr:
                    logger.warning(result.stderr)
                return False
                
        except Exception as e:
            logger.error(f"Failed to create sample PDF: {e}")
            return False
    
    def check_server(self) -> bool:
        """Check if the API server is running."""
        logger.info(f"Checking if API server is running at {self.base_url}...")
        
        try:
            import requests
            response = requests.get(f"{self.base_url}/docs", timeout=5)
            if response.status_code == 200:
                logger.info("✓ API server is running")
                return True
            else:
                logger.error(f"✗ Server responded with status {response.status_code}")
                return False
        except ImportError:
            logger.warning("requests not available, skipping server check")
            return True
        except Exception as e:
            logger.error(f"✗ Failed to connect to server: {e}")
            logger.info("Please start the server with: python3 main.py")
            return False
    
    def run_curl_tests(self) -> bool:
        """Run curl-based tests."""
        logger.info("Running curl-based API tests...")
        
        if not os.path.exists('test_api_curl.sh'):
            logger.error("test_api_curl.sh not found")
            return False
        
        try:
            # Make script executable
            os.chmod('test_api_curl.sh', 0o755)
            
            result = subprocess.run(['./test_api_curl.sh'], 
                                  capture_output=True, text=True)
            
            logger.info("Curl test output:")
            if result.stdout:
                print(result.stdout)
            if result.stderr:
                print(result.stderr, file=sys.stderr)
            
            success = result.returncode == 0
            self.test_results['curl_tests'] = success
            
            if success:
                logger.info("✓ Curl tests completed successfully")
            else:
                logger.error("✗ Curl tests failed")
            
            return success
            
        except Exception as e:
            logger.error(f"Failed to run curl tests: {e}")
            self.test_results['curl_tests'] = False
            return False
    
    def run_python_tests(self) -> bool:
        """Run Python-based tests."""
        logger.info("Running Python-based API tests...")
        
        if not os.path.exists('test_api_python.py'):
            logger.error("test_api_python.py not found")
            return False
        
        try:
            result = subprocess.run([
                sys.executable, 'test_api_python.py', '--url', self.base_url
            ], capture_output=True, text=True)
            
            logger.info("Python test output:")
            if result.stdout:
                print(result.stdout)
            if result.stderr:
                print(result.stderr, file=sys.stderr)
            
            success = result.returncode == 0
            self.test_results['python_tests'] = success
            
            if success:
                logger.info("✓ Python tests completed successfully")
            else:
                logger.error("✗ Python tests failed")
            
            return success
            
        except Exception as e:
            logger.error(f"Failed to run Python tests: {e}")
            self.test_results['python_tests'] = False
            return False
    
    def run_quick_test(self) -> bool:
        """Run a quick test to verify basic functionality."""
        logger.info("Running quick API test...")
        
        try:
            import requests
            
            # Test server availability
            response = requests.get(f"{self.base_url}/docs", timeout=5)
            if response.status_code != 200:
                logger.error("Server not responding correctly")
                return False
            
            logger.info("✓ Quick test passed")
            return True
            
        except ImportError:
            logger.warning("requests not available for quick test")
            return True
        except Exception as e:
            logger.error(f"Quick test failed: {e}")
            return False
    
    def print_summary(self):
        """Print test results summary."""
        logger.info("=" * 60)
        logger.info("API Test Results Summary")
        logger.info("=" * 60)
        
        if not self.test_results:
            logger.info("No tests were run")
            return
        
        for test_name, result in self.test_results.items():
            status = "✓ PASS" if result else "✗ FAIL"
            test_display = test_name.replace('_', ' ').title()
            logger.info(f"{test_display:.<30} {status}")
        
        total_tests = len(self.test_results)
        passed_tests = sum(self.test_results.values())
        
        logger.info("-" * 60)
        logger.info(f"Total Tests: {total_tests}")
        logger.info(f"Passed: {passed_tests}")
        logger.info(f"Failed: {total_tests - passed_tests}")
        
        if passed_tests == total_tests:
            logger.info("🎉 All tests passed!")
        else:
            logger.warning("⚠️  Some tests failed")
        
        logger.info("=" * 60)
    
    def run_all_tests(self, skip_server_check: bool = False) -> bool:
        """Run all tests in sequence."""
        logger.info("=" * 60)
        logger.info("Starting Comprehensive API Test Suite")
        logger.info("=" * 60)
        
        # Check dependencies
        if not self.check_dependencies():
            logger.error("Dependency check failed")
            return False
        
        # Check Python packages
        if not self.check_python_packages():
            logger.warning("Some Python packages missing, continuing anyway...")
        
        # Create sample PDF
        self.create_sample_pdf()
        
        # Check server
        if not skip_server_check:
            if not self.check_server():
                logger.error("Server check failed")
                return False
        
        # Run tests
        success = True
        
        # Quick test first
        if not self.run_quick_test():
            success = False
        
        # Run Python tests (more reliable)
        if not self.run_python_tests():
            success = False
        
        # Run curl tests (if available)
        if self.check_command('curl') and self.check_command('jq'):
            if not self.run_curl_tests():
                success = False
        else:
            logger.warning("curl or jq not available, skipping curl tests")
        
        # Print summary
        self.print_summary()
        
        return success


def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Run comprehensive API tests")
    parser.add_argument("--url", default="http://localhost:8000", 
                       help="API base URL")
    parser.add_argument("--skip-server-check", action="store_true",
                       help="Skip server availability check")
    parser.add_argument("--quick-only", action="store_true",
                       help="Run only quick tests")
    parser.add_argument("--python-only", action="store_true",
                       help="Run only Python tests")
    parser.add_argument("--curl-only", action="store_true",
                       help="Run only curl tests")
    parser.add_argument("--verbose", "-v", action="store_true",
                       help="Verbose output")
    
    args = parser.parse_args()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # Create test runner
    runner = APITestRunner(base_url=args.url)
    
    # Run specific tests based on arguments
    if args.quick_only:
        success = runner.run_quick_test()
    elif args.python_only:
        runner.check_dependencies()
        runner.create_sample_pdf()
        if not args.skip_server_check:
            runner.check_server()
        success = runner.run_python_tests()
    elif args.curl_only:
        runner.check_dependencies()
        runner.create_sample_pdf()
        if not args.skip_server_check:
            runner.check_server()
        success = runner.run_curl_tests()
    else:
        # Run all tests
        success = runner.run_all_tests(skip_server_check=args.skip_server_check)
    
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
