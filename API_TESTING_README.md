# PDF Processing API Testing Suite

This directory contains comprehensive testing tools for the PDF processing API, including both automated test scripts and manual testing commands.

## 📁 Files Overview

### Test Scripts
- **`test_api_python.py`** - Comprehensive Python-based API testing
- **`test_api_curl.sh`** - Shell script with curl-based API testing
- **`run_api_tests.py`** - Orchestrates all tests and handles setup
- **`create_sample_pdf.py`** - Creates sample PDF documents for testing

### Documentation
- **`API_CURL_COMMANDS.md`** - Ready-to-use curl commands for manual testing
- **`API_TESTING_README.md`** - This comprehensive guide

### Sample Data
- **`documents/sample_test_document.pdf`** - Generated test PDF (created automatically)

## 🚀 Quick Start

### Step 1: Set Up Redis (Required)
```bash
# Option A: Automated Docker setup (Recommended)
python3 setup_docker_redis.py --mode redis-only

# Option B: Manual Redis installation
# macOS: brew install redis && brew services start redis
# Ubuntu: sudo apt-get install redis-server && sudo systemctl start redis-server

# Option C: Simple Docker command
docker run -d --name redis-test -p 6379:6379 redis:alpine
```

### Step 2: Run Tests
```bash
# Run comprehensive test suite
python3 run_api_tests.py

# Run with verbose output
python3 run_api_tests.py --verbose

# Run against different server
python3 run_api_tests.py --url http://your-server:8000
```

### Option 2: Run Python Tests Only
```bash
# Run Python-based tests
python3 test_api_python.py

# With custom settings
python3 test_api_python.py --url http://localhost:8000 --verbose
```

### Option 3: Run curl Tests Only
```bash
# Make script executable and run
chmod +x test_api_curl.sh
./test_api_curl.sh
```

### Option 4: Manual Testing with curl
```bash
# See API_CURL_COMMANDS.md for detailed examples
# Quick test:
curl -X POST \
  -H "X-API-Key: a614192a822e6daef18a68029396879632c768dac57fe826043cf785bcdf519a7" \
  -F "file=@documents/sample_test_document.pdf" \
  "http://localhost:8000/upload-pdf"
```

## 📋 Prerequisites

### Required
- **Python 3.7+** - For running Python test scripts
- **curl** - For curl-based testing (usually pre-installed)
- **Redis** - Required for Celery task processing (see setup options below)
- **API Server Running** - The main application server must be running

### Redis Setup Options
Choose one of these options to get Redis running:

#### Option A: Docker (Recommended)
```bash
# Automated setup
python3 setup_docker_redis.py --mode redis-only

# Manual Docker
docker run -d --name redis-test -p 6379:6379 redis:alpine
```

#### Option B: Native Installation
```bash
# macOS
brew install redis
brew services start redis

# Ubuntu/Debian
sudo apt-get install redis-server
sudo systemctl start redis-server

# Windows
# Download from: https://github.com/microsoftarchive/redis/releases
```

#### Option C: Docker Compose
```bash
# Use existing docker-compose.yml
docker-compose up redis -d

# Or use Redis-only compose file
docker-compose -f docker-compose.redis-only.yml up -d
```

### Optional (for better experience)
- **jq** - For JSON formatting in curl tests
  ```bash
  # macOS
  brew install jq

  # Ubuntu/Debian
  sudo apt-get install jq
  ```

- **Python packages** for PDF creation:
  ```bash
  pip install reportlab  # or fpdf2
  ```

### Python Dependencies
```bash
pip install requests  # Required for Python tests
```

## 🔧 Configuration

### API Key
The tests use the API key from your `.env` file:
```
API_KEY=a614192a822e6daef18a68029396879632c768dac57fe826043cf785bcdf519a7
```

### Server URL
Default: `http://localhost:8000`

You can override this:
```bash
python3 test_api_python.py --url http://your-server:8000
```

## 📊 Test Coverage

### 1. Server Health Check
- ✅ API server availability
- ✅ Documentation endpoint access
- ✅ Basic connectivity

### 2. PDF Upload & Processing
- ✅ File upload with authentication
- ✅ Task creation and ID generation
- ✅ Document record creation
- ✅ Processing status monitoring

### 3. Task Monitoring
- ✅ Celery task status tracking
- ✅ Progress monitoring
- ✅ Completion detection
- ✅ Error handling

### 4. Document Retrieval
- ✅ Processing status queries
- ✅ Document chunk retrieval
- ✅ Content validation

### 5. Search Functionality
- ✅ Semantic search queries
- ✅ Result validation
- ✅ Response generation

### 6. Error Handling
- ✅ Invalid API keys
- ✅ Missing files
- ✅ Server errors
- ✅ Timeout scenarios

## 📈 Test Results

### Successful Test Output
```
2024-01-15 10:30:00 [INFO] ================================================================================
2024-01-15 10:30:00 [INFO] Starting PDF Processing API Test Suite
2024-01-15 10:30:00 [INFO] ================================================================================
2024-01-15 10:30:00 [INFO] ✓ API server is running
2024-01-15 10:30:00 [INFO] ✓ Sample PDF created successfully
2024-01-15 10:30:00 [INFO] ✓ PDF uploaded successfully
2024-01-15 10:30:00 [INFO]   Task ID: abc123-def456-ghi789
2024-01-15 10:30:00 [INFO]   Document ID: 42
2024-01-15 10:30:00 [INFO] ⏳ Still processing... (5s elapsed)
2024-01-15 10:30:00 [INFO] ✓ Task completed successfully!
2024-01-15 10:30:00 [INFO] ✓ Retrieved 5 chunks
2024-01-15 10:30:00 [INFO] ✓ Search completed in 0.85s
2024-01-15 10:30:00 [INFO] ================================================================================
2024-01-15 10:30:00 [INFO] Test Results Summary
2024-01-15 10:30:00 [INFO] ================================================================================
2024-01-15 10:30:00 [INFO] Server Check...................... ✓ PASS
2024-01-15 10:30:00 [INFO] PDF Upload........................ ✓ PASS
2024-01-15 10:30:00 [INFO] Task Monitoring................... ✓ PASS
2024-01-15 10:30:00 [INFO] Processing Status................. ✓ PASS
2024-01-15 10:30:00 [INFO] Document Chunks................... ✓ PASS
2024-01-15 10:30:00 [INFO] Search Test....................... ✓ PASS
2024-01-15 10:30:00 [INFO] 🎉 All tests passed!
```

## 🛠 Troubleshooting

### Common Issues

#### 1. Server Not Running
```
✗ Failed to connect to server: Connection refused
```
**Solution**: Start the API server:
```bash
python3 main.py
```

#### 2. Missing API Key
```
✗ Upload failed with status 403
Response: {"detail":"Invalid or missing API key"}
```
**Solution**: Check your `.env` file has the correct `API_KEY`

#### 3. PDF Creation Failed
```
reportlab not installed. Install with: pip install reportlab
```
**Solution**: Install PDF library:
```bash
pip install reportlab
# or
pip install fpdf2
```

#### 4. Dependencies Missing
```
Missing dependencies:
  - curl (curl command-line tool)
```
**Solution**: Install missing tools:
```bash
# macOS
brew install curl jq

# Ubuntu/Debian
sudo apt-get install curl jq
```

### Debug Mode

Run tests with verbose output for debugging:
```bash
python3 test_api_python.py --verbose
python3 run_api_tests.py --verbose
```

### Manual Verification

If automated tests fail, try manual verification:
```bash
# 1. Check server
curl http://localhost:8000/docs

# 2. Test upload
curl -X POST \
  -H "X-API-Key: your_api_key" \
  -F "file=@documents/sample_test_document.pdf" \
  "http://localhost:8000/upload-pdf"
```

## 🔄 Continuous Testing

### Integration with CI/CD
```yaml
# Example GitHub Actions workflow
name: API Tests
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Setup Python
        uses: actions/setup-python@v2
        with:
          python-version: '3.8'
      - name: Install dependencies
        run: |
          pip install -r requirements.txt
          sudo apt-get install jq
      - name: Start API server
        run: python3 main.py &
      - name: Wait for server
        run: sleep 10
      - name: Run tests
        run: python3 run_api_tests.py
```

### Automated Monitoring
```bash
# Run tests every hour
0 * * * * cd /path/to/project && python3 run_api_tests.py --quick-only
```

## 📚 Additional Resources

- **API Documentation**: Visit `http://localhost:8000/docs` when server is running
- **curl Command Reference**: See `API_CURL_COMMANDS.md`
- **Python API Client**: Use `test_api_python.py` as a reference implementation

## 🤝 Contributing

When adding new tests:

1. **Add to Python test suite**: Extend `test_api_python.py`
2. **Add curl examples**: Update `API_CURL_COMMANDS.md`
3. **Update test runner**: Modify `run_api_tests.py` if needed
4. **Document changes**: Update this README

## 📝 License

These testing tools are part of the main project and follow the same license terms.
