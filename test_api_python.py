#!/usr/bin/env python3
"""
Python API Testing Script for PDF Processing

This script provides comprehensive testing of the PDF upload and processing API
with detailed error handling, progress monitoring, and result validation.
"""

import os
import sys
import time
import json
import requests
import logging
from pathlib import Path
from typing import Optional, Dict, Any
import argparse

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)


class APITester:
    """API testing class for PDF processing endpoints."""

    def __init__(self, base_url: str = "http://localhost:8000", api_key: str = None):
        """Initialize the API tester."""
        self.base_url = base_url.rstrip('/')
        self.api_key = api_key or "a614192a822e6daef18a68029396879632c768dac57fe826043cf785bcdf519a7"
        self.session = requests.Session()
        self.session.headers.update({
            'X-API-Key': self.api_key
        })

        # Test results storage
        self.results = {
            'server_check': False,
            'pdf_upload': False,
            'task_monitoring': False,
            'processing_status': False,
            'document_chunks': False,
            'search_test': False,
            'task_id': None,
            'doc_id': None
        }

    def check_server(self) -> bool:
        """Check if the API server is running."""
        logger.info("Checking if API server is running...")
        try:
            response = self.session.get(f"{self.base_url}/docs", timeout=10)
            if response.status_code == 200:
                logger.info("✓ API server is running")
                self.results['server_check'] = True
                return True
            else:
                logger.error(f"✗ Server responded with status {response.status_code}")
                return False
        except requests.exceptions.RequestException as e:
            logger.error(f"✗ Failed to connect to server: {e}")
            logger.info("Please start the server with: python3 main.py")
            return False

    def check_redis(self) -> bool:
        """Check if Redis is available."""
        logger.info("Checking Redis availability...")
        try:
            import socket
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5)
            result = sock.connect_ex(('localhost', 6379))
            sock.close()

            if result == 0:
                logger.info("✓ Redis is running")
                return True
            else:
                logger.warning("✗ Redis is not running on localhost:6379")
                logger.info("Redis is required for Celery task processing.")
                logger.info("Start Redis with:")
                logger.info("  macOS: brew install redis && brew services start redis")
                logger.info("  Ubuntu: sudo apt-get install redis-server && sudo systemctl start redis-server")
                logger.info("  Docker: docker run -d -p 6379:6379 redis:alpine")
                return False
        except Exception as e:
            logger.warning(f"Could not check Redis: {e}")
            return False

    def create_sample_pdf(self, pdf_path: str) -> bool:
        """Create a sample PDF for testing if it doesn't exist."""
        if os.path.exists(pdf_path):
            logger.info(f"✓ Sample PDF already exists: {pdf_path}")
            return True

        logger.info("Creating sample PDF for testing...")

        # Create documents directory
        os.makedirs(os.path.dirname(pdf_path), exist_ok=True)

        # Sample content
        sample_content = """# Sample Test Document for API Testing

## Introduction

This is a comprehensive test document created specifically for validating
the PDF processing API endpoints. It contains multiple sections with
varied content to test the chunking and embedding generation capabilities.

## Section 1: System Overview

The document processing system is designed to handle PDF files and extract
meaningful information through advanced natural language processing techniques.
Key components include:

- PDF text extraction and parsing
- Intelligent semantic chunking
- High-dimensional embedding generation
- Knowledge graph construction and entity extraction
- Real-time processing status monitoring

## Section 2: Technical Architecture

The system employs a microservices architecture with the following components:

### API Layer
- FastAPI-based REST endpoints
- Authentication and authorization
- File upload handling
- Status monitoring

### Processing Pipeline
- Asynchronous task processing with Celery
- PDF parsing with multiple fallback strategies
- Semantic chunking using AI models
- Embedding generation with OpenAI models

### Data Storage
- SingleStore database for vector storage
- Document metadata and processing status
- Knowledge graph entities and relationships

## Section 3: API Endpoints

The system provides several key endpoints:

1. `/upload-pdf` - Upload and initiate processing
2. `/processing-status/{doc_id}` - Monitor processing progress
3. `/task-status/{task_id}` - Check Celery task status
4. `/doc-chunks` - Retrieve processed document chunks
5. `/kag-search` - Perform semantic search queries

## Section 4: Testing Scenarios

This document tests various processing scenarios:

- Multi-section document structure
- Technical terminology and concepts
- Lists and structured content
- Code-like formatting and technical details

## Conclusion

This test document provides comprehensive coverage for validating
the document processing pipeline from upload through search functionality.
The varied content ensures robust testing of all system components.
"""

        # Try to create PDF using available methods
        try:
            # Method 1: Try using reportlab if available
            try:
                from reportlab.lib.pagesizes import letter
                from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer
                from reportlab.lib.styles import getSampleStyleSheet

                doc = SimpleDocTemplate(pdf_path, pagesize=letter)
                styles = getSampleStyleSheet()
                story = []

                # Split content into paragraphs and add to PDF
                for line in sample_content.split('\n'):
                    if line.strip():
                        if line.startswith('#'):
                            # Header
                            level = len(line) - len(line.lstrip('#'))
                            style = styles['Heading1'] if level <= 2 else styles['Heading2']
                            story.append(Paragraph(line.lstrip('# '), style))
                        else:
                            # Regular paragraph
                            story.append(Paragraph(line, styles['Normal']))
                        story.append(Spacer(1, 12))

                doc.build(story)
                logger.info(f"✓ Sample PDF created using reportlab: {pdf_path}")
                return True

            except ImportError:
                pass

            # Method 2: Create a simple text file and suggest manual conversion
            txt_path = pdf_path.replace('.pdf', '.txt')
            with open(txt_path, 'w', encoding='utf-8') as f:
                f.write(sample_content)

            logger.warning(f"Created text file: {txt_path}")
            logger.warning("Please convert to PDF manually or install reportlab:")
            logger.warning("pip install reportlab")
            logger.warning("Or use online converter to create PDF from the text file")

            return False

        except Exception as e:
            logger.error(f"Failed to create sample PDF: {e}")
            return False

    def upload_pdf(self, pdf_path: str) -> bool:
        """Upload a PDF file to the API."""
        if not os.path.exists(pdf_path):
            logger.error(f"PDF file not found: {pdf_path}")
            return False

        logger.info(f"Uploading PDF file: {pdf_path}")

        try:
            with open(pdf_path, 'rb') as f:
                files = {'file': (os.path.basename(pdf_path), f, 'application/pdf')}
                response = self.session.post(f"{self.base_url}/upload-pdf", files=files)

            if response.status_code == 202:
                data = response.json()
                logger.info("✓ PDF uploaded successfully")
                logger.info(f"  Task ID: {data.get('task_id')}")
                logger.info(f"  Document ID: {data.get('doc_id')}")
                logger.info(f"  Status: {data.get('status')}")

                self.results['pdf_upload'] = True
                self.results['task_id'] = data.get('task_id')
                self.results['doc_id'] = data.get('doc_id')
                return True
            else:
                logger.error(f"✗ Upload failed with status {response.status_code}")
                logger.error(f"Response: {response.text}")
                return False

        except Exception as e:
            logger.error(f"✗ Upload failed: {e}")
            return False

    def check_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """Check the status of a Celery task."""
        if not task_id:
            logger.warning("No task ID provided")
            return None

        logger.info(f"Checking task status: {task_id}")

        try:
            response = self.session.get(f"{self.base_url}/task-status/{task_id}")

            if response.status_code == 200:
                data = response.json()
                status = data.get('status', 'unknown')

                logger.info(f"✓ Task status: {status}")
                if 'message' in data:
                    logger.info(f"  Message: {data['message']}")
                if 'current' in data and 'total' in data:
                    logger.info(f"  Progress: {data['current']}/{data['total']}")

                self.results['task_monitoring'] = True
                return data
            else:
                logger.error(f"✗ Failed to get task status: {response.status_code}")
                logger.error(f"Response: {response.text}")
                return None

        except Exception as e:
            logger.error(f"✗ Task status check failed: {e}")
            return None

    def check_processing_status(self, doc_id: int) -> Optional[Dict[str, Any]]:
        """Check the processing status of a document."""
        if not doc_id:
            logger.warning("No document ID provided")
            return None

        logger.info(f"Checking processing status for document: {doc_id}")

        try:
            response = self.session.get(f"{self.base_url}/processing-status/{doc_id}")

            if response.status_code == 200:
                data = response.json()
                current_step = data.get('currentStep', 'unknown')
                file_name = data.get('fileName', 'unknown')

                logger.info(f"✓ Processing status: {current_step}")
                logger.info(f"  File name: {file_name}")

                self.results['processing_status'] = True
                return data
            else:
                logger.error(f"✗ Failed to get processing status: {response.status_code}")
                logger.error(f"Response: {response.text}")
                return None

        except Exception as e:
            logger.error(f"✗ Processing status check failed: {e}")
            return None

    def wait_for_completion(self, task_id: str, max_wait: int = 300) -> bool:
        """Wait for task completion with timeout."""
        if not task_id:
            logger.warning("No task ID provided for monitoring")
            return False

        logger.info(f"Waiting for task completion (max {max_wait}s)...")

        start_time = time.time()
        check_interval = 5

        while time.time() - start_time < max_wait:
            status_data = self.check_task_status(task_id)

            if status_data:
                status = status_data.get('status', '').upper()

                if status == 'SUCCESS':
                    logger.info("✓ Task completed successfully!")
                    return True
                elif status == 'FAILURE':
                    logger.error("✗ Task failed!")
                    if 'error' in status_data:
                        logger.error(f"Error: {status_data['error']}")
                    return False
                elif status in ['PENDING', 'PROGRESS', 'STARTED']:
                    elapsed = int(time.time() - start_time)
                    logger.info(f"⏳ Still processing... ({elapsed}s elapsed)")
                else:
                    logger.warning(f"Unknown status: {status}")

            time.sleep(check_interval)

        logger.warning("⏰ Timeout waiting for task completion")
        return False

    def get_document_chunks(self, doc_id: int) -> bool:
        """Retrieve document chunks."""
        if not doc_id:
            logger.warning("No document ID provided")
            return False

        logger.info(f"Retrieving document chunks for document: {doc_id}")

        try:
            response = self.session.get(f"{self.base_url}/doc-chunks", params={'doc_id': doc_id})

            if response.status_code == 200:
                data = response.json()
                chunks = data.get('chunks', [])

                logger.info(f"✓ Retrieved {len(chunks)} chunks")

                if chunks:
                    # Show preview of first chunk
                    first_chunk = chunks[0].get('content', '')[:200]
                    logger.info(f"  First chunk preview: {first_chunk}...")

                self.results['document_chunks'] = True
                return True
            else:
                logger.error(f"✗ Failed to get chunks: {response.status_code}")
                logger.error(f"Response: {response.text}")
                return False

        except Exception as e:
            logger.error(f"✗ Chunks retrieval failed: {e}")
            return False

    def test_search(self, query: str = "document processing") -> bool:
        """Test the search functionality."""
        logger.info(f"Testing search with query: '{query}'")

        try:
            payload = {
                "query": query,
                "top_k": 3,
                "debug": False
            }

            response = self.session.post(f"{self.base_url}/kag-search", json=payload)

            if response.status_code == 200:
                data = response.json()
                results = data.get('results', [])
                execution_time = data.get('execution_time', 0)
                generated_response = data.get('generated_response', '')

                logger.info(f"✓ Search completed in {execution_time:.2f}s")
                logger.info(f"  Found {len(results)} results")

                if generated_response:
                    preview = generated_response[:200]
                    logger.info(f"  Generated response preview: {preview}...")

                self.results['search_test'] = True
                return True
            else:
                logger.error(f"✗ Search failed: {response.status_code}")
                logger.error(f"Response: {response.text}")
                return False

        except Exception as e:
            logger.error(f"✗ Search test failed: {e}")
            return False

    def run_full_test(self, pdf_path: str = "documents/sample_test_document.pdf") -> bool:
        """Run the complete test suite."""
        logger.info("=" * 60)
        logger.info("Starting PDF Processing API Test Suite")
        logger.info("=" * 60)

        # Step 1: Check server
        if not self.check_server():
            return False

        # Step 1.5: Check Redis (required for Celery)
        redis_available = self.check_redis()
        if not redis_available:
            logger.warning("Redis is not available. PDF upload may fail.")
            logger.warning("Continuing with tests, but expect upload failures...")

        # Step 2: Create/check sample PDF
        if not os.path.exists(pdf_path):
            if not self.create_sample_pdf(pdf_path):
                logger.error("Cannot proceed without a test PDF file")
                return False

        # Step 3: Upload PDF
        if not self.upload_pdf(pdf_path):
            return False

        # Step 4: Monitor processing
        task_id = self.results.get('task_id')
        doc_id = self.results.get('doc_id')

        if task_id:
            # Wait for completion
            if self.wait_for_completion(task_id):
                logger.info("Processing completed successfully!")

                # Step 5: Test additional endpoints
                if doc_id:
                    self.check_processing_status(doc_id)
                    self.get_document_chunks(doc_id)

                # Step 6: Test search
                self.test_search()
            else:
                logger.warning("Processing did not complete, but upload was successful")

        # Print summary
        self.print_summary()

        # Return overall success
        return all([
            self.results['server_check'],
            self.results['pdf_upload']
        ])

    def print_summary(self):
        """Print test results summary."""
        logger.info("=" * 60)
        logger.info("Test Results Summary")
        logger.info("=" * 60)

        for test_name, result in self.results.items():
            if test_name in ['task_id', 'doc_id']:
                continue

            status = "✓ PASS" if result else "✗ FAIL"
            test_display = test_name.replace('_', ' ').title()
            logger.info(f"{test_display:.<30} {status}")

        if self.results['task_id']:
            logger.info(f"Task ID: {self.results['task_id']}")
        if self.results['doc_id']:
            logger.info(f"Document ID: {self.results['doc_id']}")

        logger.info("=" * 60)


def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Test PDF Processing API")
    parser.add_argument("--url", default="http://localhost:8000", help="API base URL")
    parser.add_argument("--api-key", help="API key (uses default if not provided)")
    parser.add_argument("--pdf", default="documents/sample_test_document.pdf", help="PDF file path")
    parser.add_argument("--verbose", "-v", action="store_true", help="Verbose output")

    args = parser.parse_args()

    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    # Create tester instance
    tester = APITester(base_url=args.url, api_key=args.api_key)

    # Run tests
    success = tester.run_full_test(args.pdf)

    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
