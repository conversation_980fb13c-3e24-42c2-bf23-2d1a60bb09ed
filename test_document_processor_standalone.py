#!/usr/bin/env python3
"""
Standalone test script for DocumentProcessor functionality.

This script tests the DocumentProcessor logic without importing the actual main.py,
avoiding dependency issues while still providing comprehensive test coverage.
"""

import os
import sys
import json
import tempfile
import unittest
from unittest.mock import Mock, patch, MagicMock, mock_open
import logging
import re
import numpy as np

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(name)s: %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)


class MockDocumentProcessor:
    """
    Mock implementation of DocumentProcessor for testing.
    This replicates the key functionality without external dependencies.
    """
    
    def __init__(self):
        """Initialize the DocumentProcessor with necessary configurations."""
        # Validate environment variables
        self.gemini_api_key = os.getenv("GEMINI_API_KEY")
        self.openai_api_key = os.getenv("OPENAI_API_KEY")
        self.project_id = os.getenv("PROJECT_ID")
        self.embedding_model = os.getenv("EMBEDDING_MODEL", "text-embedding-ada-002")
        
        self._validate_env_vars()
        
        # Mock API clients
        self.openai_client = Mock()
        self.gemini_model = Mock()
    
    def _validate_env_vars(self):
        """Validate that all required environment variables are set."""
        if not self.gemini_api_key or not self.openai_api_key or not self.project_id:
            raise ValueError("API keys or PROJECT_ID are missing in the .env file.")
    
    def get_chunks(self, pdf_path: str) -> str:
        """
        Process a PDF file into markdown with semantic chunks.
        """
        logger.info("Reading PDF file: %s", pdf_path)
        
        # Mock PDF reading
        full_text = "Sample PDF content for testing"
        
        # Mock Gemini response
        mock_response = """
        # Sample Document
        
        <chunk>
        This is the first chunk of the document containing important information
        about the topic. It has enough content to be meaningful for embedding.
        </chunk>
        
        <chunk>
        This is the second chunk with different content that discusses another
        aspect of the topic. It also contains sufficient text for processing.
        </chunk>
        """
        
        # Save markdown content
        base_name = os.path.splitext(os.path.basename(pdf_path))[0]
        md_path = os.path.join(os.path.dirname(pdf_path), f"{base_name}.md")
        
        with open(md_path, "w", encoding="utf-8") as md_file:
            md_file.write(mock_response)
            
        logger.info("Markdown file written: %s", md_path)
        return md_path
    
    def create_embeddings(self, input_markdown_file, output_json_file):
        """
        Create embeddings from markdown chunks and save to JSON.
        """
        # Read the markdown file content
        try:
            with open(input_markdown_file, 'r', encoding='utf-8') as f:
                markdown_text = f.read()
        except Exception as e:
            logging.error(f"Failed to read input file {input_markdown_file}: {e}")
            return

        # Extract chunks marked with <chunk> tags
        chunk_pattern = r'<chunk>(.*?)</chunk>'
        chunks = re.findall(chunk_pattern, markdown_text, re.DOTALL)
        
        if not chunks:
            logger.warning("No chunks found with <chunk> tags. Using whole text as single chunk.")
            chunks = [markdown_text]

        embeddings_data = []
        for idx, chunk in enumerate(chunks):
            # Skip empty chunks if any
            chunk = chunk.strip()
            if not chunk or chunk.isspace():
                continue
                
            # Mock embedding generation
            embedding_vector = [0.1] * 1536  # Mock 1536-dimensional embedding
            
            # Convert to float32 numpy array for SingleStore compatibility
            embedding_array = np.array(embedding_vector, dtype=np.float32)
            
            # Verify the embedding has correct dimensions
            expected_dims = 1536
            if len(embedding_array) != expected_dims:
                logger.error(
                    "Embedding dimension mismatch: got %d dimensions, schema expects 1536.",
                    len(embedding_array)
                )
                raise ValueError("Embedding dimension mismatch")
            
            # Convert numpy array back to list for JSON serialization
            embedding_list = embedding_array.tolist()
            
            record = {
                "chunk_index": idx,
                "text": chunk,
                "embedding": embedding_list
            }
            embeddings_data.append(record)

        # Save the list of embedding records to a JSON file
        try:
            with open(output_json_file, 'w', encoding='utf-8') as out_f:
                json.dump(embeddings_data, out_f, ensure_ascii=False, indent=2)
            logger.info(f"Saved {len(embeddings_data)} embeddings to {output_json_file}")
        except Exception as e:
            logging.error(f"Failed to write embeddings to {output_json_file}: {e}")
    
    def insert_embeddings_to_db(self, embeddings_file: str, document_id: int) -> None:
        """
        Mock database insertion of embeddings.
        """
        try:
            # Load embeddings from JSON file
            with open(embeddings_file, 'r') as f:
                data = json.load(f)
                
            logger.info("Inserting embeddings from file: %s", embeddings_file)
            
            # Mock database operations
            logger.info("Successfully inserted %d chunks for document_id %d", len(data), document_id)
        
        except Exception as e:
            logger.error("Failed to insert embeddings: %s", str(e))
            raise
    
    def process_document(self, input_path: str, document_id: int) -> dict:
        """
        Process a document end-to-end: create chunks, embeddings, and insert into database.
        """
        try:
            results = {
                'input_path': input_path,
                'document_id': document_id,
                'markdown_path': None,
                'embeddings_path': None,
                'chunks_count': 0
            }
            
            # Process PDF if needed
            if input_path.endswith('.pdf'):
                base_name = os.path.splitext(os.path.basename(input_path))[0]
                md_path = os.path.join(os.path.dirname(input_path), f"{base_name}.md")
                
                # If markdown exists, use it; otherwise process PDF
                if os.path.exists(md_path):
                    logger.info("Using existing markdown file: %s", md_path)
                    results['markdown_path'] = md_path
                else:
                    results['markdown_path'] = self.get_chunks(input_path)
            else:
                results['markdown_path'] = input_path
            
            # Create embeddings
            embeddings_path = f"{os.path.splitext(results['markdown_path'])[0]}_embeddings.json"
            results['embeddings_path'] = embeddings_path
            self.create_embeddings(results['markdown_path'], embeddings_path)
            
            # Insert into database
            self.insert_embeddings_to_db(results['embeddings_path'], document_id)
            
            # Get chunks count
            with open(results['embeddings_path'], 'r') as f:
                results['chunks_count'] = len(json.load(f))
            
            return results
            
        except Exception as e:
            logger.error("Document processing failed: %s", str(e))
            raise


class TestDocumentProcessor(unittest.TestCase):
    """Unit tests for DocumentProcessor class."""
    
    def setUp(self):
        """Set up test fixtures before each test method."""
        # Mock environment variables
        self.env_patcher = patch.dict(os.environ, {
            'GEMINI_API_KEY': 'test_gemini_key_12345678',
            'OPENAI_API_KEY': 'test_openai_key_12345678',
            'PROJECT_ID': 'test_project_id',
            'EMBEDDING_MODEL': 'text-embedding-ada-002'
        })
        self.env_patcher.start()
        
    def tearDown(self):
        """Clean up after each test method."""
        self.env_patcher.stop()
    
    def test_init_success(self):
        """Test successful initialization of DocumentProcessor."""
        processor = MockDocumentProcessor()
        
        # Verify attributes
        self.assertEqual(processor.gemini_api_key, 'test_gemini_key_12345678')
        self.assertEqual(processor.openai_api_key, 'test_openai_key_12345678')
        self.assertEqual(processor.project_id, 'test_project_id')
        self.assertEqual(processor.embedding_model, 'text-embedding-ada-002')
    
    def test_init_missing_env_vars(self):
        """Test initialization failure with missing environment variables."""
        with patch.dict(os.environ, {}, clear=True):
            with self.assertRaises(ValueError) as context:
                MockDocumentProcessor()
            self.assertIn("API keys or PROJECT_ID are missing", str(context.exception))
    
    def test_get_chunks_success(self):
        """Test successful PDF chunk processing."""
        processor = MockDocumentProcessor()
        
        # Test the method
        with tempfile.NamedTemporaryFile(suffix='.pdf', delete=False) as temp_pdf:
            try:
                result_path = processor.get_chunks(temp_pdf.name)
                
                # Verify result
                expected_md_path = temp_pdf.name.replace('.pdf', '.md')
                self.assertEqual(result_path, expected_md_path)
                
                # Verify file was created
                self.assertTrue(os.path.exists(result_path))
                
                # Verify content
                with open(result_path, 'r') as f:
                    content = f.read()
                    self.assertIn('<chunk>', content)
                
                # Clean up
                os.unlink(result_path)
            finally:
                os.unlink(temp_pdf.name)
    
    def test_create_embeddings_success(self):
        """Test successful embedding creation from markdown chunks."""
        # Setup test data
        markdown_content = """
        Some intro text
        <chunk>First chunk content with meaningful text</chunk>
        Some middle text
        <chunk>Second chunk content with more text</chunk>
        Some end text
        """
        
        processor = MockDocumentProcessor()
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.md', delete=False) as temp_md:
            temp_md.write(markdown_content)
            temp_md.flush()
            
            with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as temp_json:
                try:
                    processor.create_embeddings(temp_md.name, temp_json.name)
                    
                    # Verify output file exists and has correct structure
                    self.assertTrue(os.path.exists(temp_json.name))
                    
                    with open(temp_json.name, 'r') as f:
                        result_data = json.load(f)
                    
                    self.assertEqual(len(result_data), 2)
                    self.assertIn('chunk_index', result_data[0])
                    self.assertIn('text', result_data[0])
                    self.assertIn('embedding', result_data[0])
                    self.assertEqual(len(result_data[0]['embedding']), 1536)
                    
                finally:
                    os.unlink(temp_md.name)
                    os.unlink(temp_json.name)
    
    def test_end_to_end_processing(self):
        """Test complete document processing pipeline."""
        processor = MockDocumentProcessor()
        
        # Create a sample markdown file for testing
        test_content = """
        # Test Document
        
        <chunk>
        This is a test chunk with sample content for processing.
        It contains enough text to be meaningful for embedding generation.
        </chunk>
        
        <chunk>
        This is another test chunk with different content.
        It also has sufficient text for the embedding process.
        </chunk>
        """
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.md', delete=False) as temp_file:
            temp_file.write(test_content)
            temp_file.flush()
            
            try:
                # Test the complete processing pipeline
                result = processor.process_document(temp_file.name, document_id=123)
                
                # Verify result structure
                self.assertIn('input_path', result)
                self.assertIn('document_id', result)
                self.assertIn('markdown_path', result)
                self.assertIn('embeddings_path', result)
                self.assertIn('chunks_count', result)
                
                self.assertEqual(result['document_id'], 123)
                self.assertEqual(result['chunks_count'], 2)
                
                # Verify files were created
                self.assertTrue(os.path.exists(result['embeddings_path']))
                
                # Clean up generated files
                if os.path.exists(result['embeddings_path']):
                    os.unlink(result['embeddings_path'])
                    
            finally:
                os.unlink(temp_file.name)


def run_tests(verbose=False):
    """Run the test suite."""
    if verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    logger.info("Adding tests...")
    suite.addTests(loader.loadTestsFromTestCase(TestDocumentProcessor))
    
    runner = unittest.TextTestRunner(verbosity=2 if verbose else 1)
    result = runner.run(suite)
    
    return result.wasSuccessful()


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Test DocumentProcessor class (standalone)")
    parser.add_argument("--verbose", "-v", action="store_true", help="Verbose output")
    
    args = parser.parse_args()
    
    logger.info("=" * 80)
    logger.info("DocumentProcessor Standalone Test Suite")
    logger.info("=" * 80)
    
    success = run_tests(verbose=args.verbose)
    
    if success:
        logger.info("All tests passed! ✅")
        sys.exit(0)
    else:
        logger.error("Some tests failed! ❌")
        sys.exit(1)
