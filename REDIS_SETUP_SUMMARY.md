# Redis Setup Summary

This document provides a quick reference for all Redis setup options available for the PDF processing API.

## 🎯 The Problem
The API upload was failing with:
```
Error 61 connecting to localhost:6379. Connection refused.
```

This happens because the API uses **Celery** for asynchronous PDF processing, and Celery requires **Redis** as a message broker.

## 🚀 Quick Solutions

### 1. Fastest: Docker One-Liner
```bash
docker run -d --name redis-test -p 6379:6379 redis:alpine
```

### 2. Automated: Setup Script
```bash
python3 setup_docker_redis.py --mode redis-only
```

### 3. Native Installation
```bash
# macOS
brew install redis && brew services start redis

# Ubuntu
sudo apt-get install redis-server && sudo systemctl start redis-server
```

### 4. Docker Compose
```bash
docker-compose -f docker-compose.redis-only.yml up -d
```

## 📁 Available Files

### Setup Scripts
| File | Purpose |
|------|---------|
| `setup_redis.py` | Native Redis installation helper |
| `setup_docker_redis.py` | Docker-based Redis setup |
| `test_redis_connection.py` | Test Redis connectivity |

### Docker Compose Files
| File | Use Case | What's Included |
|------|----------|-----------------|
| `docker-compose.redis-only.yml` | Testing only | Redis only |
| `docker-compose.dev.yml` | Development | Redis + monitoring tools |
| `docker-compose.yml` | Full stack | Redis + API + Celery |

### Configuration
| File | Purpose |
|------|---------|
| `redis.conf` | Redis configuration for development |
| `DOCKER_REDIS_SETUP.md` | Detailed Docker setup guide |

## 🔍 Verification

### Check if Redis is Running
```bash
# Quick check
python3 test_redis_connection.py

# Manual check
redis-cli ping
# Should return: PONG

# Port check
telnet localhost 6379
# Should connect successfully
```

### Test API After Redis Setup
```bash
# Run comprehensive API tests
python3 test_api_python.py

# Or just test upload
curl -X POST \
  -H "X-API-Key: a614192a822e6daef18a68029396879632c768dac57fe826043cf785bcdf519a7" \
  -F "file=@documents/sample_test_document.pdf" \
  "http://localhost:8000/upload-pdf"
```

## 🛠 Troubleshooting

### Common Issues

#### Port 6379 Already in Use
```bash
# Find what's using the port
lsof -i :6379

# Stop existing Redis
brew services stop redis  # macOS
sudo systemctl stop redis-server  # Ubuntu
```

#### Docker Permission Issues
```bash
# Add user to docker group
sudo usermod -aG docker $USER
# Then logout and login
```

#### Redis Not Responding
```bash
# Check Redis logs
docker logs redis-test

# Restart Redis
docker restart redis-test
```

## 📊 Monitoring Options

### Basic Monitoring
```bash
# Redis CLI
redis-cli monitor

# Docker stats
docker stats redis-test
```

### Advanced Monitoring (Development Mode)
```bash
# Start development environment
python3 setup_docker_redis.py --mode dev

# Access monitoring tools:
# - Redis Commander: http://localhost:8081 (admin/admin)
# - RedisInsight: http://localhost:8001
```

## 🔄 Management Commands

### Start/Stop Services
```bash
# Start Redis only
python3 setup_docker_redis.py --mode redis-only

# Stop all services
python3 setup_docker_redis.py --stop

# Check status
python3 setup_docker_redis.py --status
```

### Data Management
```bash
# Backup Redis data
docker run --rm -v redis_data:/data -v $(pwd):/backup alpine tar czf /backup/redis-backup.tar.gz -C /data .

# Clear all Redis data
redis-cli flushall

# Remove Docker volumes
docker volume rm redis_data
```

## 🎯 Recommended Workflow

### For Testing
1. **Start Redis**: `python3 setup_docker_redis.py --mode redis-only`
2. **Verify**: `python3 test_redis_connection.py`
3. **Test API**: `python3 test_api_python.py`

### For Development
1. **Start Dev Environment**: `python3 setup_docker_redis.py --mode dev`
2. **Access Monitoring**: Open `http://localhost:8081`
3. **Develop**: Use Redis Commander to inspect data

### For Production
1. **Use External Redis**: Configure production Redis service
2. **Update Environment**: Set `REDIS_URL` in production
3. **Monitor**: Use production monitoring tools

## 🔗 Related Documentation

- **`DOCKER_REDIS_SETUP.md`** - Detailed Docker setup guide
- **`API_TESTING_README.md`** - Complete API testing guide
- **`API_CURL_COMMANDS.md`** - Manual testing commands

## 💡 Tips

1. **Always verify Redis is running** before testing the API
2. **Use Docker for consistency** across different environments
3. **Monitor Redis memory usage** in development
4. **Backup Redis data** before major changes
5. **Use development mode** for debugging and monitoring

## 🆘 Still Having Issues?

1. **Check the logs**: `docker logs redis-test`
2. **Verify network connectivity**: `telnet localhost 6379`
3. **Test with simple commands**: `redis-cli ping`
4. **Try different setup method**: Native vs Docker
5. **Check firewall settings**: Ensure port 6379 is open

## ✅ Success Indicators

You know Redis is working correctly when:
- ✅ `python3 test_redis_connection.py` passes all tests
- ✅ `redis-cli ping` returns `PONG`
- ✅ PDF upload returns task_id instead of connection error
- ✅ API tests complete successfully

---

**Remember**: Redis is required for the PDF processing API to work. Without it, file uploads will fail with connection errors.
