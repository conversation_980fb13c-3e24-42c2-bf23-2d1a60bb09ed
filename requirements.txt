aiohappyeyeballs==2.4.6
aiohttp==3.11.12
aiosignal==1.3.2
amqp==5.3.1
annotated-types==0.7.0
anyio==4.8.0
attrs==25.1.0
beautifulsoup4==4.13.3
billiard==4.2.1
build==1.2.2.post1
cachetools==5.5.1
celery==5.3.6
certifi==2025.1.31
charset-normalizer==3.4.1
click==8.1.8
click-didyoumean==0.3.1
click-plugins==1.1.1
click-repl==0.3.0
dataclasses-json==0.6.7
Deprecated==1.2.18
dirtyjson==1.0.8
distro==1.9.0
fastapi==0.115.8
filetype==1.2.0
frozenlist==1.5.0
fsspec==2025.2.0
google-ai-generativelanguage==0.6.15
google-api-core==2.24.1
google-api-python-client==2.160.0
google-auth==2.38.0
google-auth-httplib2==0.2.0
google-generativeai==0.8.4
googleapis-common-protos==1.66.0
greenlet==3.1.1
grpcio==1.70.0
grpcio-status==1.70.0
h11==0.14.0
httpcore==1.0.7
httplib2==0.22.0
httptools==0.6.4
httpx==0.27.2
idna==3.10
jiter==0.8.2
joblib==1.4.2
kombu==5.4.2
llama-cloud==0.1.12
llama-cloud-services==0.6.1
llama-index-core==0.12.19
llama-index-readers-file==0.4.5
marshmallow==3.26.1
moviepy==1.0.3
multidict==6.1.0
mypy-extensions==1.0.0
mysql-connector-python==9.2.0
nest-asyncio==1.6.0
networkx==3.4.2
nltk==3.9.1
numpy==2.2.3
openai==1.61.1
packaging==24.2
pandas==2.2.3
parsimonious==0.10.0
pillow==11.1.0
prompt_toolkit==3.0.50
propcache==0.2.1
proto-plus==1.26.0
protobuf==5.29.3
pyasn1==0.6.1
pyasn1_modules==0.4.1
pydantic==2.10.6
pydantic_core==2.27.2
pydub==0.25.1
PyJWT==2.10.1
PyMuPDF==1.23.8
PyMuPDFb==1.23.7
pyparsing==3.2.1
pypdf==5.3.0
PyPDF2==3.0.1
pyproject_hooks==1.2.0
python-dateutil==2.9.0.post0
python-dotenv==1.0.1
python-multipart==0.0.6
pytz==2025.1
PyYAML==6.0.1
redis==5.0.1
regex==2024.11.6
requests==2.32.3
rsa==4.9
setuptools==75.8.0
singlestoredb==1.11.0
six==1.17.0
sniffio==1.3.1
soupsieve==2.6
SQLAlchemy==2.0.38
sqlparams==6.2.0
starlette==0.45.3
striprtf==0.0.26
tenacity==9.0.0
tiktoken==0.9.0
tqdm==4.67.1
typing-inspect==0.9.0
typing_extensions==4.12.2
tzdata==2025.1
uritemplate==4.1.1
urllib3==2.3.0
uvicorn==0.34.0
uvloop==0.21.0
vine==5.1.0
watchfiles==1.0.4
wcwidth==0.2.13
websockets==14.2
wheel==0.45.1
wrapt==1.17.2
yarl==1.18.3