#!/usr/bin/env python3
"""
Test script to verify SingleStore database connection
"""
import os
import mysql.connector
from mysql.connector import Error
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_connection():
    """Test database connection with current credentials"""
    
    # Get credentials from environment
    host = os.getenv("SINGLESTORE_HOST")
    port = int(os.getenv("SINGLESTORE_PORT", "3306"))
    user = os.getenv("SINGLESTORE_USER")
    password = os.getenv("SINGLESTORE_PASSWORD")
    database = os.getenv("SINGLESTORE_DATABASE")
    
    print("=== SingleStore Connection Test ===")
    print(f"Host: {host}")
    print(f"Port: {port}")
    print(f"User: {user}")
    print(f"Password: {'*' * len(password) if password else 'NOT SET'}")
    print(f"Database: {database}")
    print()
    
    # Check if all required variables are set
    if not all([host, user, password, database]):
        print("❌ ERROR: Missing required environment variables!")
        missing = []
        if not host: missing.append("SINGLESTORE_HOST")
        if not user: missing.append("SINGLESTORE_USER")
        if not password: missing.append("SINGLESTORE_PASSWORD")
        if not database: missing.append("SINGLESTORE_DATABASE")
        print(f"Missing: {', '.join(missing)}")
        return False
    
    try:
        print("🔄 Attempting to connect...")
        
        # Try to establish connection
        connection = mysql.connector.connect(
            host=host,
            port=port,
            user=user,
            password=password,
            database=database,
            connect_timeout=10
        )
        
        if connection.is_connected():
            print("✅ Connection successful!")
            
            # Get database info
            cursor = connection.cursor()
            
            # Test basic query
            cursor.execute("SELECT VERSION()")
            version = cursor.fetchone()
            print(f"Database Version: {version[0]}")
            
            # Get current user and database
            cursor.execute("SELECT USER(), DATABASE()")
            user_db = cursor.fetchone()
            print(f"Connected as: {user_db[0]}")
            print(f"Current database: {user_db[1]}")
            
            # Test table access
            cursor.execute("SHOW TABLES")
            tables = cursor.fetchall()
            print(f"Available tables: {len(tables)}")
            for table in tables:
                print(f"  - {table[0]}")
            
            cursor.close()
            connection.close()
            print("✅ Connection test completed successfully!")
            return True
            
    except Error as e:
        print(f"❌ Connection failed!")
        print(f"Error Code: {e.errno}")
        print(f"Error Message: {e.msg}")
        
        # Provide specific guidance based on error
        if e.errno == 1045:
            print("\n🔍 This is an authentication error. Possible causes:")
            print("1. Incorrect username or password")
            print("2. User doesn't exist in the database")
            print("3. User doesn't have permission to access the database")
            print("4. IP address is not whitelisted")
        elif e.errno == 2003:
            print("\n🔍 This is a connection error. Possible causes:")
            print("1. Incorrect host or port")
            print("2. Database server is down")
            print("3. Network connectivity issues")
            print("4. Firewall blocking the connection")
        
        return False
        
    except Exception as e:
        print(f"❌ Unexpected error: {str(e)}")
        return False

if __name__ == "__main__":
    success = test_connection()
    exit(0 if success else 1)
