#!/usr/bin/env python3
"""
Simple test runner for DocumentProcessor tests.

This script provides an easy way to run the DocumentProcessor tests
with different configurations and provides clear output.
"""

import os
import sys
import subprocess
import logging
from pathlib import Path

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)


def check_dependencies():
    """Check if required dependencies are available."""
    logger.info("Checking dependencies...")
    
    required_modules = [
        'unittest',
        'unittest.mock',
        'tempfile',
        'json',
        'os',
        'sys'
    ]
    
    missing_modules = []
    for module in required_modules:
        try:
            __import__(module)
            logger.debug(f"✓ {module}")
        except ImportError:
            missing_modules.append(module)
            logger.error(f"✗ {module}")
    
    if missing_modules:
        logger.error(f"Missing required modules: {missing_modules}")
        return False
    
    logger.info("All dependencies are available ✓")
    return True


def check_environment():
    """Check if the environment is properly set up."""
    logger.info("Checking environment setup...")
    
    # Check if main.py exists
    if not os.path.exists('main.py'):
        logger.error("main.py not found in current directory")
        return False
    
    # Check if test file exists
    if not os.path.exists('test_document_processor.py'):
        logger.error("test_document_processor.py not found")
        return False
    
    logger.info("Environment setup is correct ✓")
    return True


def run_tests(test_type="all", verbose=False):
    """Run the DocumentProcessor tests."""
    logger.info(f"Running {test_type} tests...")
    
    cmd = [sys.executable, 'test_document_processor.py']
    
    if test_type == "unit":
        cmd.append('--unit-only')
    elif test_type == "integration":
        cmd.append('--integration-only')
    
    if verbose:
        cmd.append('--verbose')
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        # Print output
        if result.stdout:
            print(result.stdout)
        if result.stderr:
            print(result.stderr, file=sys.stderr)
        
        return result.returncode == 0
        
    except Exception as e:
        logger.error(f"Failed to run tests: {e}")
        return False


def create_sample_test_data():
    """Create sample test data if needed."""
    logger.info("Creating sample test data...")
    
    # Create documents directory if it doesn't exist
    docs_dir = Path("documents")
    docs_dir.mkdir(exist_ok=True)
    
    # Create a simple test markdown file
    test_md_content = """# Sample Test Document

This is a sample document for testing the DocumentProcessor.

<chunk>
This is the first chunk of content. It contains information about
the document processing pipeline and how it works with different
types of content. This chunk has enough text to be meaningful
for embedding generation and testing purposes.
</chunk>

## Section 2

<chunk>
This is the second chunk with different content. It discusses
another aspect of document processing, including how chunks
are extracted and processed. This chunk also contains sufficient
text for proper testing of the embedding functionality.
</chunk>

## Conclusion

<chunk>
This final chunk wraps up the document with concluding remarks
about the document processing system. It demonstrates how the
chunking system works with multiple sections and provides
a complete example for testing purposes.
</chunk>
"""
    
    test_md_path = docs_dir / "sample_test_document.md"
    with open(test_md_path, 'w', encoding='utf-8') as f:
        f.write(test_md_content)
    
    logger.info(f"Created sample test document: {test_md_path}")
    return str(test_md_path)


def main():
    """Main function to run the test suite."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Run DocumentProcessor tests")
    parser.add_argument(
        "--type", 
        choices=["all", "unit", "integration"], 
        default="all",
        help="Type of tests to run"
    )
    parser.add_argument(
        "--verbose", "-v", 
        action="store_true", 
        help="Verbose output"
    )
    parser.add_argument(
        "--skip-checks", 
        action="store_true", 
        help="Skip dependency and environment checks"
    )
    parser.add_argument(
        "--create-sample-data", 
        action="store_true", 
        help="Create sample test data"
    )
    
    args = parser.parse_args()
    
    logger.info("=" * 80)
    logger.info("DocumentProcessor Test Runner")
    logger.info("=" * 80)
    
    # Create sample data if requested
    if args.create_sample_data:
        create_sample_test_data()
    
    # Run checks unless skipped
    if not args.skip_checks:
        if not check_dependencies():
            logger.error("Dependency check failed")
            sys.exit(1)
        
        if not check_environment():
            logger.error("Environment check failed")
            sys.exit(1)
    
    # Run tests
    success = run_tests(args.type, args.verbose)
    
    if success:
        logger.info("=" * 80)
        logger.info("All tests completed successfully! ✅")
        logger.info("=" * 80)
        sys.exit(0)
    else:
        logger.error("=" * 80)
        logger.error("Some tests failed! ❌")
        logger.error("=" * 80)
        sys.exit(1)


if __name__ == "__main__":
    main()
