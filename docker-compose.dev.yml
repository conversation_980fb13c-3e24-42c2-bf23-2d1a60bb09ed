# Development Docker Compose file
# Includes Redis and optional monitoring tools for development
# 
# Usage:
#   docker-compose -f docker-compose.dev.yml up -d
#   docker-compose -f docker-compose.dev.yml down

version: '3.8'

services:
  redis:
    image: redis:7-alpine
    container_name: kag-redis-dev
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./redis.conf:/usr/local/etc/redis/redis.conf:ro
    command: redis-server /usr/local/etc/redis/redis.conf
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 5s
      timeout: 3s
      retries: 5
      start_period: 10s
    restart: unless-stopped
    networks:
      - kag-network

  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: kag-redis-ui
    hostname: redis-commander
    ports:
      - "8081:8081"
    environment:
      - REDIS_HOSTS=local:redis:6379
      - HTTP_USER=admin
      - HTTP_PASSWORD=admin
    depends_on:
      redis:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - kag-network

  # Optional: Redis monitoring with RedisInsight
  redisinsight:
    image: redislabs/redisinsight:latest
    container_name: kag-redisinsight
    ports:
      - "8001:8001"
    volumes:
      - redisinsight_data:/db
    depends_on:
      redis:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - kag-network

volumes:
  redis_data:
    driver: local
  redisinsight_data:
    driver: local

networks:
  kag-network:
    driver: bridge
