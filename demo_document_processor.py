#!/usr/bin/env python3
"""
Demo script for DocumentProcessor functionality.

This script demonstrates how to use the DocumentProcessor class
with sample data and provides examples of different usage patterns.
"""

import os
import sys
import tempfile
import logging
from pathlib import Path

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)


def create_sample_documents():
    """Create sample documents for testing."""
    logger.info("Creating sample documents...")
    
    # Create documents directory
    docs_dir = Path("documents")
    docs_dir.mkdir(exist_ok=True)
    
    # Sample markdown document with chunks
    sample_md = """# Sample Research Paper

## Abstract

<chunk>
This research paper explores the applications of artificial intelligence
in document processing systems. We examine various approaches to text
extraction, semantic chunking, and embedding generation for knowledge
retrieval applications. Our findings suggest that modern AI models
can significantly improve the accuracy and efficiency of document
processing pipelines.
</chunk>

## Introduction

<chunk>
Document processing has become increasingly important in the digital age.
Organizations need to extract meaningful information from large volumes
of unstructured text data. Traditional approaches often fall short when
dealing with complex document structures and semantic relationships.
This paper presents a comprehensive approach using state-of-the-art
natural language processing techniques.
</chunk>

## Methodology

<chunk>
Our methodology consists of three main phases: document parsing,
semantic chunking, and embedding generation. We use advanced PDF
parsing techniques to extract text while preserving document structure.
The semantic chunking phase identifies coherent sections of text that
maintain topical consistency. Finally, we generate high-dimensional
embeddings using transformer-based models for efficient retrieval.
</chunk>

## Results

<chunk>
Our experiments show significant improvements in document retrieval
accuracy compared to traditional keyword-based approaches. The semantic
chunking strategy resulted in 85% better precision in information
retrieval tasks. The embedding-based similarity search demonstrated
robust performance across different document types and domains.
</chunk>

## Conclusion

<chunk>
This work demonstrates the effectiveness of AI-powered document
processing systems. The combination of intelligent chunking and
semantic embeddings provides a powerful foundation for knowledge
management applications. Future work will explore multi-modal
document processing and real-time streaming capabilities.
</chunk>
"""
    
    # Save sample document
    sample_path = docs_dir / "sample_research_paper.md"
    with open(sample_path, 'w', encoding='utf-8') as f:
        f.write(sample_md)
    
    logger.info(f"Created sample document: {sample_path}")
    return str(sample_path)


def demo_basic_usage():
    """Demonstrate basic DocumentProcessor usage."""
    logger.info("=" * 60)
    logger.info("Demo: Basic DocumentProcessor Usage")
    logger.info("=" * 60)
    
    # Import the standalone mock processor for demo
    from test_document_processor_standalone import MockDocumentProcessor
    
    # Set up environment variables for demo
    os.environ.update({
        'GEMINI_API_KEY': 'demo_gemini_key_12345678',
        'OPENAI_API_KEY': 'demo_openai_key_12345678',
        'PROJECT_ID': 'demo_project_id',
        'EMBEDDING_MODEL': 'text-embedding-ada-002'
    })
    
    try:
        # Initialize processor
        logger.info("Initializing DocumentProcessor...")
        processor = MockDocumentProcessor()
        logger.info("✓ DocumentProcessor initialized successfully")
        
        # Create sample document
        sample_doc = create_sample_documents()
        
        # Process the document
        logger.info("Processing document...")
        result = processor.process_document(sample_doc, document_id=1001)
        
        # Display results
        logger.info("Processing completed successfully!")
        logger.info(f"Input path: {result['input_path']}")
        logger.info(f"Document ID: {result['document_id']}")
        logger.info(f"Markdown path: {result['markdown_path']}")
        logger.info(f"Embeddings path: {result['embeddings_path']}")
        logger.info(f"Chunks count: {result['chunks_count']}")
        
        # Show embedding file content
        if os.path.exists(result['embeddings_path']):
            import json
            with open(result['embeddings_path'], 'r') as f:
                embeddings_data = json.load(f)
            
            logger.info("\nEmbedding data preview:")
            for i, chunk_data in enumerate(embeddings_data[:2]):  # Show first 2 chunks
                logger.info(f"Chunk {i}:")
                logger.info(f"  Text preview: {chunk_data['text'][:100]}...")
                logger.info(f"  Embedding dimensions: {len(chunk_data['embedding'])}")
        
        return result
        
    except Exception as e:
        logger.error(f"Demo failed: {e}")
        return None


def demo_error_handling():
    """Demonstrate error handling scenarios."""
    logger.info("=" * 60)
    logger.info("Demo: Error Handling")
    logger.info("=" * 60)
    
    from test_document_processor_standalone import MockDocumentProcessor
    
    # Test 1: Missing environment variables
    logger.info("Test 1: Missing environment variables")
    original_env = os.environ.copy()
    try:
        # Clear environment variables
        for key in ['GEMINI_API_KEY', 'OPENAI_API_KEY', 'PROJECT_ID']:
            if key in os.environ:
                del os.environ[key]
        
        try:
            processor = MockDocumentProcessor()
            logger.error("Expected error but initialization succeeded")
        except ValueError as e:
            logger.info(f"✓ Correctly caught error: {e}")
    finally:
        # Restore environment
        os.environ.update(original_env)
    
    # Test 2: Processing non-existent file
    logger.info("\nTest 2: Processing non-existent file")
    os.environ.update({
        'GEMINI_API_KEY': 'demo_gemini_key_12345678',
        'OPENAI_API_KEY': 'demo_openai_key_12345678',
        'PROJECT_ID': 'demo_project_id'
    })
    
    try:
        processor = MockDocumentProcessor()
        result = processor.process_document("non_existent_file.md", document_id=999)
        logger.error("Expected error but processing succeeded")
    except Exception as e:
        logger.info(f"✓ Correctly caught error: {e}")


def demo_chunk_extraction():
    """Demonstrate chunk extraction from different document formats."""
    logger.info("=" * 60)
    logger.info("Demo: Chunk Extraction")
    logger.info("=" * 60)
    
    from test_document_processor_standalone import MockDocumentProcessor
    
    # Set up environment
    os.environ.update({
        'GEMINI_API_KEY': 'demo_gemini_key_12345678',
        'OPENAI_API_KEY': 'demo_openai_key_12345678',
        'PROJECT_ID': 'demo_project_id'
    })
    
    processor = MockDocumentProcessor()
    
    # Test different markdown formats
    test_cases = [
        {
            'name': 'Document with multiple chunks',
            'content': '''
            # Title
            <chunk>First chunk content</chunk>
            ## Section
            <chunk>Second chunk content</chunk>
            '''
        },
        {
            'name': 'Document without chunk tags',
            'content': '''
            # Title
            This is a document without explicit chunk tags.
            It should be processed as a single chunk.
            '''
        },
        {
            'name': 'Document with empty chunks',
            'content': '''
            # Title
            <chunk>Valid chunk content</chunk>
            <chunk></chunk>
            <chunk>Another valid chunk</chunk>
            '''
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        logger.info(f"\nTest case {i}: {test_case['name']}")
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.md', delete=False) as temp_file:
            temp_file.write(test_case['content'])
            temp_file.flush()
            
            try:
                # Create embeddings
                embeddings_path = f"{temp_file.name}_embeddings.json"
                processor.create_embeddings(temp_file.name, embeddings_path)
                
                # Check results
                import json
                with open(embeddings_path, 'r') as f:
                    embeddings_data = json.load(f)
                
                logger.info(f"  Extracted {len(embeddings_data)} chunks")
                for j, chunk in enumerate(embeddings_data):
                    preview = chunk['text'][:50].replace('\n', ' ')
                    logger.info(f"    Chunk {j}: {preview}...")
                
                # Clean up
                os.unlink(embeddings_path)
                
            except Exception as e:
                logger.error(f"  Error processing test case: {e}")
            finally:
                os.unlink(temp_file.name)


def main():
    """Main demo function."""
    logger.info("DocumentProcessor Demo Script")
    logger.info("=" * 80)
    
    try:
        # Run basic usage demo
        result = demo_basic_usage()
        
        # Clean up generated files
        if result and result.get('embeddings_path') and os.path.exists(result['embeddings_path']):
            os.unlink(result['embeddings_path'])
            logger.info("Cleaned up generated files")
        
        # Run error handling demo
        demo_error_handling()
        
        # Run chunk extraction demo
        demo_chunk_extraction()
        
        logger.info("=" * 80)
        logger.info("Demo completed successfully! ✅")
        
    except Exception as e:
        logger.error(f"Demo failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
